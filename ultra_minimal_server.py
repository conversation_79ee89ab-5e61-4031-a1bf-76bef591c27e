#!/usr/bin/env python3
"""
Ultra-minimal server to isolate the performance issue
"""

from http.server import H<PERSON>PServer, SimpleHTTPRequestHandler
import os

class UltraMinimalHandler(SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory="./roraftp_optimized/shared_files", **kwargs)
    
    def do_GET(self):
        """Ultra-simple GET handler."""
        if self.path == '/':
            # Send simple HTML response
            html = b'''<!DOCTYPE html>
<html>
<head><title>Ultra Minimal Server</title></head>
<body>
<h1>Ultra Minimal Server</h1>
<p>This is working!</p>
</body>
</html>'''
            
            self.send_response(200)
            self.send_header('Content-Type', 'text/html')
            self.send_header('Content-Length', str(len(html)))
            self.end_headers()
            self.wfile.write(html)
        else:
            # Use standard file serving
            super().do_GET()

def main():
    # Change to the shared files directory
    os.chdir("./roraftp_optimized")
    
    server = HTTPServer(('localhost', 7777), UltraMinimalHandler)
    print("Ultra-minimal server running on http://localhost:7777")
    print("Press Ctrl+C to stop")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped")

if __name__ == "__main__":
    main()
