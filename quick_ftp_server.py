#!/usr/bin/env python3
"""
RoraFTP LAN Edition - Maximum Speed & Seamless Experience
Optimized for local network file sharing with zero friction

Requirements: Python 3.6+
Optional: qrcode, zeroconf (auto-installed)
"""

import sys

# Check Python version first
if sys.version_info < (3, 6):
    try:
        print("❌ Python 3.6 or higher is required")
    except UnicodeEncodeError:
        print("Python 3.6 or higher is required")
    print(f"   Current version: {sys.version_info.major}.{sys.version_info.minor}")
    print("   Please upgrade Python and try again")
    sys.exit(1)

import os
import sys
import socket
import threading
import time
import webbrowser
import logging
import mimetypes
import json
import subprocess
import shutil
from pathlib import Path
from http.server import HTTPServer, SimpleHTTPRequestHandler
from socketserver import ThreadingMixIn
from urllib.parse import quote, unquote
import argparse

# Safe printing function for Unicode characters
def safe_print(message):
    """Print message with fallback for Unicode encoding issues."""
    try:
        print(message)
    except UnicodeEncodeError:
        # Remove Unicode characters and print fallback
        fallback = message.encode('ascii', errors='ignore').decode('ascii')
        print(fallback)

# Auto-install and import optional dependencies
def safe_import(module_name, pip_name=None):
    """Safely import module, auto-install if needed"""
    if pip_name is None:
        pip_name = module_name

    try:
        return __import__(module_name)
    except ImportError:
        safe_print(f"📦 Installing {module_name}...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", pip_name
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            safe_print(f"✅ {module_name} installed successfully")
            return __import__(module_name)
        except Exception as e:
            safe_print(f"⚠️ Could not install {module_name}: {e}")
            return None

def safe_import_from(module_name, items, pip_name=None):
    """Safely import specific items from module"""
    if pip_name is None:
        pip_name = module_name

    try:
        module = __import__(module_name, fromlist=items)
        return {item: getattr(module, item) for item in items}
    except ImportError:
        safe_print(f"📦 Installing {module_name}...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", pip_name
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            safe_print(f"✅ {module_name} installed successfully")
            module = __import__(module_name, fromlist=items)
            return {item: getattr(module, item) for item in items}
        except Exception as e:
            safe_print(f"⚠️ Could not install {module_name}: {e}")
            return {item: None for item in items}

# Try to import optional dependencies
safe_print("⚡ RoraFTP LAN Edition - Setting up environment...")

# QR Code support
qrcode = safe_import('qrcode', 'qrcode[pil]')

# mDNS support
zeroconf_imports = safe_import_from('zeroconf', ['ServiceInfo', 'Zeroconf'])
ServiceInfo = zeroconf_imports.get('ServiceInfo')
Zeroconf = zeroconf_imports.get('Zeroconf')

safe_print("✅ Environment setup complete!")

# LAN Performance Configuration
LAN_CHUNK_SIZE = 8 * 1024 * 1024  # 8MB chunks for gigabit LAN
MAX_CONCURRENT = 100  # Allow many concurrent transfers
CACHE_TTL = 10  # Short cache for responsiveness
BUFFER_SIZE = 4 * 1024 * 1024  # 4MB socket buffers

def optimize_lan_socket(sock):
    """Optimize socket for maximum LAN performance"""
    try:
        # More conservative settings for Windows
        import platform
        if platform.system() == 'Windows':
            # Windows-friendly settings
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 1024 * 1024)  # 1MB
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 1024 * 1024)  # 1MB
        else:
            # Aggressive settings for Linux/Mac
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, BUFFER_SIZE)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, BUFFER_SIZE)
        
        # Universal low latency settings
        sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
        
        # Conservative keep-alive for Windows compatibility  
        if hasattr(socket, 'TCP_KEEPIDLE'):
            sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPIDLE, 60)
            sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPINTVL, 10)
            sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPCNT, 3)
            
    except OSError:
        pass  # Some optimizations may not be available

class FastDirectoryCache:
    """Ultra-fast directory caching for LAN responsiveness"""
    def __init__(self):
        self.cache = {}
        self.last_update = {}
    
    def get_listing(self, path):
        """Get cached directory listing with short TTL"""
        path_str = str(path)
        now = time.time()
        
        if (path_str in self.cache and 
            now - self.last_update.get(path_str, 0) < CACHE_TTL):
            return self.cache[path_str]
        
        # Fast directory scan
        try:
            entries = []
            with os.scandir(path) as iterator:
                for entry in iterator:
                    try:
                        stat_result = entry.stat(follow_symlinks=False)
                        entries.append({
                            'name': entry.name,
                            'is_dir': entry.is_dir(follow_symlinks=False),
                            'size': stat_result.st_size if not entry.is_dir() else 0,
                            'mtime': stat_result.st_mtime,
                            'ext': Path(entry.name).suffix.lower()
                        })
                    except OSError:
                        continue  # Skip problematic files
            
            self.cache[path_str] = entries
            self.last_update[path_str] = now
            return entries
            
        except OSError:
            return []
    
    def invalidate(self, path):
        """Invalidate cache for a path"""
        path_str = str(path)
        if path_str in self.cache:
            del self.cache[path_str]
        if path_str in self.last_update:
            del self.last_update[path_str]

class LANOptimizedServer(ThreadingMixIn, HTTPServer):
    """High-performance server optimized for LAN"""
    allow_reuse_address = True
    daemon_threads = True
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        optimize_lan_socket(self.socket)
    
    def handle_error(self, request, client_address):
        """Handle errors gracefully, especially connection resets"""
        import sys
        exc_type, exc_value, exc_traceback = sys.exc_info()
        
        # Ignore common connection errors that aren't real problems
        if exc_type in (ConnectionResetError, BrokenPipeError, ConnectionAbortedError):
            return  # Silently ignore connection reset errors
        
        if hasattr(exc_value, 'winerror') and exc_value.winerror == 10054:
            return  # Ignore Windows connection reset
            
        # Log other errors
        print(f"⚠️ Connection error from {client_address}: {exc_type.__name__}")
    
    def finish_request(self, request, client_address):
        """Finish request with better error handling"""
        try:
            super().finish_request(request, client_address)
        except (ConnectionResetError, BrokenPipeError, ConnectionAbortedError):
            # Client disconnected - not an error
            pass
        except OSError as e:
            if e.errno == 10054:  # Windows connection reset
                pass
            else:
                print(f"⚠️ Socket error: {e}")
        except Exception as e:
            print(f"⚠️ Request error: {e}")

class LANFileHandler(SimpleHTTPRequestHandler):
    """Ultra-fast file handler optimized for LAN speeds"""
    
    def __init__(self, *args, **kwargs):
        self.share_path = kwargs.pop('share_path', '.')
        self.directory_cache = FastDirectoryCache()
        super().__init__(*args, directory=self.share_path, **kwargs)
    
    def log_message(self, format, *args):
        """Minimal logging for performance"""
        pass  # Disable logging for maximum speed and fewer errors
    
    def handle(self):
        """Handle request with better error handling"""
        try:
            super().handle()
        except (ConnectionResetError, BrokenPipeError, ConnectionAbortedError):
            # Client disconnected - normal behavior, not an error
            pass
        except OSError as e:
            if getattr(e, 'winerror', None) == 10054:
                # Windows connection reset - ignore
                pass
            else:
                print(f"⚠️ Connection error: {e}")
        except Exception as e:
            print(f"⚠️ Handler error: {e}")
    
    def send_error(self, code, message=None, explain=None):
        """Send error with connection reset protection"""
        try:
            super().send_error(code, message, explain)
        except (ConnectionResetError, BrokenPipeError, ConnectionAbortedError):
            pass  # Client already disconnected
        except OSError as e:
            if getattr(e, 'winerror', None) != 10054:
                print(f"⚠️ Error sending error response: {e}")
    
    def end_headers(self):
        """End headers with error protection"""
        try:
            super().end_headers()
        except (ConnectionResetError, BrokenPipeError, ConnectionAbortedError):
            pass
    
    def do_GET(self):
        """Ultra-fast file serving with LAN optimizations"""
        try:
            if self.path == '/api/stats':
                self._send_stats()
                return
            elif self.path == '/api/open-explorer':
                self._open_explorer()
                return
            elif self.path == '/api/purge-directory':
                self._purge_directory()
                return
            
            # Fast path translation
            file_path = Path(self.translate_path(self.path))
            
            if file_path.is_dir():
                self._send_directory_listing(file_path)
            elif file_path.is_file():
                self._send_file_fast(file_path)
            else:
                self.send_error(404)
                
        except Exception:
            self.send_error(500)
    
    def _send_file_fast(self, file_path):
        """Send file with maximum LAN speed optimizations"""
        try:
            stat_result = file_path.stat()
            file_size = stat_result.st_size
            
            # Determine content type
            content_type, _ = mimetypes.guess_type(str(file_path))
            if content_type is None:
                content_type = 'application/octet-stream'
            
            # Handle range requests for resumable downloads
            range_header = self.headers.get('Range')
            if range_header:
                self._send_partial_content(file_path, file_size, range_header)
                return
            
            # Send full file
            self.send_response(200)
            self.send_header('Content-Type', content_type)
            self.send_header('Content-Length', str(file_size))
            self.send_header('Accept-Ranges', 'bytes')
            
            # LAN-optimized headers
            self.send_header('Connection', 'keep-alive')
            self.send_header('Keep-Alive', 'timeout=30, max=100')
            self.send_header('Cache-Control', 'public, max-age=300')
            
            self.end_headers()
            
            # Stream file with large chunks for LAN speed
            try:
                with open(file_path, 'rb') as f:
                    while True:
                        chunk = f.read(LAN_CHUNK_SIZE)
                        if not chunk:
                            break
                        try:
                            self.wfile.write(chunk)
                        except (ConnectionResetError, BrokenPipeError, ConnectionAbortedError):
                            # Client disconnected during transfer - normal
                            break
                        except OSError as e:
                            if getattr(e, 'winerror', None) == 10054:
                                break  # Windows connection reset
                            raise
            except (ConnectionResetError, BrokenPipeError, ConnectionAbortedError):
                # Client disconnected - not an error
                pass
                    
        except Exception as e:
            if not isinstance(e, (ConnectionResetError, BrokenPipeError, ConnectionAbortedError)):
                print(f"⚠️ File send error: {e}")
            try:
                self.send_error(500)
            except:
                pass
    
    def _send_partial_content(self, file_path, file_size, range_header):
        """Handle range requests for resumable downloads"""
        try:
            # Parse range header
            range_match = range_header.replace('bytes=', '').split('-')
            start = int(range_match[0]) if range_match[0] else 0
            end = int(range_match[1]) if range_match[1] else file_size - 1
            
            if start >= file_size or end >= file_size or start > end:
                self.send_error(416)
                return
            
            content_length = end - start + 1
            
            self.send_response(206)
            self.send_header('Content-Type', mimetypes.guess_type(str(file_path))[0] or 'application/octet-stream')
            self.send_header('Content-Length', str(content_length))
            self.send_header('Content-Range', f'bytes {start}-{end}/{file_size}')
            self.send_header('Accept-Ranges', 'bytes')
            self.end_headers()
            
            # Send partial content
            with open(file_path, 'rb') as f:
                f.seek(start)
                remaining = content_length
                
                while remaining > 0:
                    chunk_size = min(LAN_CHUNK_SIZE, remaining)
                    chunk = f.read(chunk_size)
                    if not chunk:
                        break
                    self.wfile.write(chunk)
                    remaining -= len(chunk)
                    
        except Exception:
            self.send_error(500)
    
    def _send_directory_listing(self, dir_path):
        """Ultra-fast directory listing with modern UI"""
        try:
            entries = self.directory_cache.get_listing(dir_path)
            
            # Separate directories and files for better UX
            directories = [e for e in entries if e['is_dir']]
            files = [e for e in entries if not e['is_dir']]
            
            # Sort by name
            directories.sort(key=lambda x: x['name'].lower())
            files.sort(key=lambda x: x['name'].lower())
            
            html = self._generate_modern_html(directories, files, dir_path)
            
            self.send_response(200)
            self.send_header('Content-Type', 'text/html; charset=utf-8')
            self.send_header('Content-Length', str(len(html.encode())))
            self.send_header('Connection', 'keep-alive')
            self.end_headers()
            
            try:
                self.wfile.write(html.encode())
            except (ConnectionResetError, BrokenPipeError, ConnectionAbortedError):
                # Client disconnected during HTML send - normal
                pass
            except OSError as e:
                if getattr(e, 'winerror', None) != 10054:
                    print(f"⚠️ Error sending HTML: {e}")
                    
        except Exception as e:
            print(f"⚠️ Directory listing error: {e}")
            try:
                self.send_error(500)
            except:
                pass
    
    def _generate_modern_html(self, directories, files, current_path):
        """Generate modern, responsive HTML interface"""
        try:
            return self._generate_full_html(directories, files, current_path)
        except Exception as e:
            print(f"⚠️ Falling back to simple HTML due to error: {e}")
            return self._generate_simple_html(directories, files, current_path)
    
    def _generate_simple_html(self, directories, files, current_path):
        """Generate simple fallback HTML interface"""
        items_html = []
        
        # Add parent directory if not root
        if str(current_path) != self.share_path:
            items_html.append('<p><a href="../">📁 ..</a></p>')
        
        # Add directories
        for dir_entry in directories:
            name = dir_entry['name']
            items_html.append(f'<p><a href="{quote(name)}/">📁 {name}</a></p>')
        
        # Add files  
        for file_entry in files:
            name = file_entry['name']
            size = self._format_size(file_entry['size'])
            items_html.append(f'<p><a href="{quote(name)}">📄 {name}</a> ({size})</p>')
        
        return f'''<!DOCTYPE html>
<html><head><title>RoraFTP</title><meta charset="utf-8"></head>
<body style="font-family: sans-serif; padding: 20px;">
<h1>⚡ RoraFTP LAN Server</h1>
<button onclick="openExplorer()" style="margin: 10px 5px; padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">📁 Open in Explorer</button>
<button onclick="purgeDirectory()" style="margin: 10px 5px; padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 5px; cursor: pointer;">🗑️ Purge Directory</button>
<form action="/upload" method="post" enctype="multipart/form-data">
<p><input type="file" name="files" multiple> <input type="submit" value="Upload"></p>
</form>
<hr>
{''.join(items_html)}
<script>
function openExplorer() {{
    fetch('/api/open-explorer')
        .then(response => response.json())
        .then(data => {{
            if (data.status === 'success') {{
                let message = 'File explorer opened!';
                if (data.system === 'Windows') message = 'File Explorer opened!';
                else if (data.system === 'Darwin') message = 'Finder opened!';
                else if (data.system === 'Linux') message = 'File Manager opened!';
                alert(message);
            }} else {{
                alert('Failed to open explorer: ' + data.message);
            }}
        }})
        .catch(error => alert('Failed to open explorer'));
}}

function purgeDirectory() {{
    if (!confirm('⚠️ WARNING: This will DELETE ALL FILES in the main directory!\\n\\nAre you absolutely sure?')) {{
        return;
    }}
    
    const confirmation = prompt('Type "DELETE" to confirm purge:');
    if (confirmation !== 'DELETE') {{
        alert('Purge cancelled - confirmation text did not match');
        return;
    }}
    
    fetch('/api/purge-directory')
        .then(response => response.json())
        .then(data => {{
            if (data.status === 'success') {{
                alert('Directory purged successfully! ' + data.message);
                location.reload();
            }} else {{
                alert('Purge failed: ' + data.message);
            }}
        }})
        .catch(error => alert('Purge failed: Network error'));
}}
</script>
</body></html>'''
    
    def _generate_full_html(self, directories, files, current_path):
        """Generate modern, responsive HTML interface"""
        # Generate file items
        items_html = []
        
        # Add parent directory link if not root
        if str(current_path) != self.share_path:
            items_html.append('''
                <div class="file-item" onclick="window.location.href='../'">
                    <div class="file-icon">📁</div>
                    <div class="file-info">
                        <div class="file-name">..</div>
                        <div class="file-meta">Parent Directory</div>
                    </div>
                </div>
            ''')
        
        # Add directories
        for dir_entry in directories:
            name = dir_entry['name']
            items_html.append(f'''
                <div class="file-item" onclick="window.location.href='{quote(name)}/'">
                    <div class="file-icon">📁</div>
                    <div class="file-info">
                        <div class="file-name">{name}</div>
                        <div class="file-meta">Folder</div>
                    </div>
                </div>
            ''')
        
        # Add files
        for file_entry in files:
            name = file_entry['name']
            size = self._format_size(file_entry['size'])
            ext = file_entry['ext']
            icon = self._get_file_icon(ext)
            
            items_html.append(f'''
                <div class="file-item" onclick="downloadFile('{quote(name)}')">
                    <div class="file-icon">{icon}</div>
                    <div class="file-info">
                        <div class="file-name">{name}</div>
                        <div class="file-meta">{size}</div>
                    </div>
                    <div class="file-actions">
                        <button onclick="event.stopPropagation(); downloadFile('{quote(name)}')" class="download-btn">⬇️</button>
                    </div>
                </div>
            ''')
        
        return f'''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RoraFTP LAN Server</title>
    <style>
        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
        
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }}
        
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        
        .header h1 {{
            font-size: 2.5em;
            font-weight: 300;
            margin-bottom: 10px;
        }}
        
        .header p {{
            opacity: 0.9;
            font-size: 1.1em;
            margin-bottom: 15px;
        }}
        
        .header-buttons {{
            display: flex;
            justify-content: center;
            gap: 10px;
            flex-wrap: wrap;
        }}
        
        .header-btn {{
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }}
        
        .header-btn:hover {{
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }}
        
        .header-btn.danger {{
            background: rgba(220, 53, 69, 0.3);
            border-color: rgba(220, 53, 69, 0.5);
        }}
        
        .header-btn.danger:hover {{
            background: rgba(220, 53, 69, 0.5);
        }}
        
        .upload-zone {{
            margin: 30px;
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            background: linear-gradient(135deg, #f6f9fc 0%, #ecf2ff 100%);
            transition: all 0.3s ease;
            cursor: pointer;
        }}
        
        .upload-zone:hover {{
            border-color: #764ba2;
            background: linear-gradient(135deg, #ecf2ff 0%, #e1edff 100%);
        }}
        
        .upload-zone.dragover {{
            border-color: #28a745;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            transform: scale(1.02);
        }}
        
        .upload-text {{
            font-size: 1.3em;
            color: #667eea;
            margin-bottom: 15px;
        }}
        
        .upload-btn {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }}
        
        .upload-btn:hover {{
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }}
        
        .file-list {{
            padding: 0 30px 30px;
        }}
        
        .file-item {{
            display: flex;
            align-items: center;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #f8f9fa;
        }}
        
        .file-item:hover {{
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            transform: translateX(5px);
        }}
        
        .file-icon {{
            font-size: 2em;
            margin-right: 15px;
            width: 50px;
            text-align: center;
        }}
        
        .file-info {{
            flex: 1;
        }}
        
        .file-name {{
            font-weight: 500;
            font-size: 1.1em;
            color: #333;
            margin-bottom: 4px;
        }}
        
        .file-meta {{
            color: #666;
            font-size: 0.9em;
        }}
        
        .file-actions {{
            opacity: 0;
            transition: opacity 0.2s ease;
        }}
        
        .file-item:hover .file-actions {{
            opacity: 1;
        }}
        
        .download-btn {{
            background: none;
            border: none;
            font-size: 1.5em;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            transition: background 0.2s ease;
        }}
        
        .download-btn:hover {{
            background: rgba(102, 126, 234, 0.1);
        }}
        
        .progress-bar {{
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 0.3s ease;
            z-index: 1000;
        }}
        
        .stats {{
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 10px;
            font-size: 0.9em;
            display: none;
        }}
        
        @media (max-width: 768px) {{
            .container {{ margin: 10px; }}
            .header {{ padding: 20px; }}
            .header h1 {{ font-size: 2em; }}
            .upload-zone {{ margin: 20px; padding: 30px; }}
            .file-list {{ padding: 0 20px 20px; }}
            .header-buttons {{ flex-direction: column; align-items: center; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⋆｡˚ ☁︎ ˚｡🅡🅞🅡🅐🅕🅣🅟⋆｡˚☽˚｡⋆ </h1>
            <p>Fuck iCloud, Google Drive and USB sticks. No security for LAN and homies only</p>
            <div class="header-buttons">
                <button onclick="openExplorer()" class="header-btn">📁 Locate main directory</button>
                <button onclick="purgeDirectory()" class="header-btn danger">🗑️ Purge main directory</button>
            </div>
        </div>
        
        <div class="upload-zone" id="uploadZone">
            <div class="upload-text"></div>
            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                (っ˘ڡ˘ς) Feed me!!!
            </button>
            <input type="file" id="fileInput" multiple style="display: none;">
        </div>
        
        <div class="file-list">
            {''.join(items_html)}
        </div>
    </div>
    
    <div class="progress-bar" id="progressBar"></div>
    <div class="stats" id="stats"></div>
    
    <script>
        const uploadZone = document.getElementById('uploadZone');
        const fileInput = document.getElementById('fileInput');
        const progressBar = document.getElementById('progressBar');
        const stats = document.getElementById('stats');
        
        // Drag and drop handling
        uploadZone.addEventListener('dragover', (e) => {{
            e.preventDefault();
            uploadZone.classList.add('dragover');
        }});
        
        uploadZone.addEventListener('dragleave', (e) => {{
            e.preventDefault();
            uploadZone.classList.remove('dragover');
        }});
        
        uploadZone.addEventListener('drop', (e) => {{
            e.preventDefault();
            uploadZone.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        }});
        
        fileInput.addEventListener('change', (e) => {{
            handleFiles(e.target.files);
        }});
        
        function handleFiles(files) {{
            if (files.length === 0) return;
            
            const formData = new FormData();
            for (let file of files) {{
                formData.append('files', file);
            }}
            
            uploadFiles(formData, files);
        }}
        
        function uploadFiles(formData, files) {{
            const xhr = new XMLHttpRequest();
            const startTime = Date.now();
            
            xhr.upload.addEventListener('progress', (e) => {{
                if (e.lengthComputable) {{
                    const progress = (e.loaded / e.total);
                    progressBar.style.transform = `scaleX(${{progress}})`;
                    
                    // Show transfer speed
                    const elapsed = (Date.now() - startTime) / 1000;
                    const speed = (e.loaded / elapsed / 1024 / 1024).toFixed(1);
                    stats.textContent = `Upload: ${{(progress * 100).toFixed(1)}}% - ${{speed}} MB/s`;
                    stats.style.display = 'block';
                }}
            }});
            
            xhr.addEventListener('load', () => {{
                if (xhr.status === 200) {{
                    setTimeout(() => location.reload(), 1000);
                    stats.textContent = 'Upload complete!';
                    setTimeout(() => stats.style.display = 'none', 3000);
                }} else {{
                    alert('Upload failed');
                    stats.style.display = 'none';
                }}
                progressBar.style.transform = 'scaleX(0)';
            }});
            
            xhr.addEventListener('error', () => {{
                alert('Upload error');
                progressBar.style.transform = 'scaleX(0)';
                stats.style.display = 'none';
            }});
            
            xhr.open('POST', '/upload');
            xhr.send(formData);
        }}
        
        function downloadFile(filename) {{
            const link = document.createElement('a');
            link.href = filename;
            link.download = decodeURIComponent(filename);
            link.click();
        }}
        
        function openExplorer() {{
            fetch('/api/open-explorer')
                .then(response => response.json())
                .then(data => {{
                    if (data.status === 'success') {{
                        // Show temporary success message
                        const btn = document.querySelector('.header-btn:not(.danger)');
                        const originalText = btn.textContent;
                        
                        // Show system-specific message
                        let message = 'Opened!';
                        if (data.system === 'Windows') message = 'File Explorer opened (somewhere...)';
                        else if (data.system === 'Darwin') message = 'Finder opened (somewhere...)';
                        else if (data.system === 'Linux') message = 'File Manager opened (somewhere...)';
                        
                        btn.textContent = message;
                        btn.style.background = 'rgba(40, 167, 69, 0.8)';
                        setTimeout(() => {{
                            btn.textContent = originalText;
                            btn.style.background = 'rgba(255, 255, 255, 0.2)';
                        }}, 2500);
                    }} else {{
                        alert('Failed to open explorer: ' + data.message);
                    }}
                }})
                .catch(error => {{
                    console.error('Error:', error);
                    alert('Failed to open explorer');
                }});
        }}
        
        function purgeDirectory() {{
            // First confirmation
            if (!confirm('⚠️ WARNING: This will DELETE ALL FILES in the main directory!\\n\\nThis action cannot be undone!\\n\\nAre you absolutely sure?')) {{
                return;
            }}
            
            }}
            
            // Show loading state
            const btn = document.querySelector('.header-btn.danger');
            const originalText = btn.textContent;
            btn.textContent = '🔥 Purging...';
            btn.disabled = true;
            
            fetch('/api/purge-directory')
                .then(response => response.json())
                .then(data => {{
                    if (data.status === 'success') {{
                        btn.textContent = '✅ Purged!';
                        btn.style.background = 'rgba(40, 167, 69, 0.8)';
                        
                        alert(`Directory purged successfully!\\n\\n${{data.message}}`);
                        setTimeout(() => location.reload(), 1500);
                    }} else {{
                        btn.textContent = originalText;
                        btn.disabled = false;
                        alert('Purge failed: ' + data.message);
                    }}
                }})
                .catch(error => {{
                    btn.textContent = originalText;
                    btn.disabled = false;
                    alert('Purge failed: Network error');
                    console.error('Error:', error);
                }});
        }}
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {{
            if (e.ctrlKey || e.metaKey) {{
                if (e.key === 'u') {{
                    e.preventDefault();
                    fileInput.click();
                }}
            }}
        }});
    </script>
</body>
</html>'''
    
    def do_POST(self):
        """Handle file uploads with progress tracking"""
        if self.path != '/upload':
            self.send_error(404)
            return
        
        try:
            content_length = int(self.headers.get('Content-Length', 0))
            if content_length == 0:
                self.send_error(400, "No content")
                return
            
            # Simple file upload handling with better error handling
            content_type = self.headers.get('Content-Type', '')
            if 'multipart/form-data' not in content_type:
                self.send_error(400, "Invalid content type")
                return
            
            # Extract boundary
            boundary = None
            for part in content_type.split(';'):
                if 'boundary=' in part:
                    boundary = part.split('boundary=')[1].strip()
                    break
            
            if not boundary:
                self.send_error(400, "No boundary found")
                return
            
            # Read upload data in chunks for large files
            uploaded_files = []
            
            try:
                data = self.rfile.read(content_length)
            except Exception as e:
                self.send_error(400, f"Failed to read upload data: {e}")
                return
            
            # Parse multipart data (simplified for performance)
            try:
                parts = data.split(f'--{boundary}'.encode())
                
                for part in parts[1:-1]:  # Skip first and last empty parts
                    if b'Content-Disposition' in part:
                        # Extract filename
                        header_end = part.find(b'\r\n\r\n')
                        if header_end == -1:
                            continue
                        
                        headers = part[:header_end].decode('utf-8', errors='ignore')
                        file_data = part[header_end + 4:]
                        
                        # Simple filename extraction
                        if 'filename=' in headers:
                            filename_start = headers.find('filename="') + 10
                            filename_end = headers.find('"', filename_start)
                            filename = headers[filename_start:filename_end]
                            
                            if filename and file_data:
                                # Save file
                                try:
                                    safe_filename = self._sanitize_filename(filename)
                                    file_path = Path(self.share_path) / safe_filename
                                    
                                    # Ensure we don't overwrite existing files
                                    counter = 1
                                    original_path = file_path
                                    while file_path.exists():
                                        stem = original_path.stem
                                        suffix = original_path.suffix
                                        file_path = original_path.parent / f"{stem}_{counter}{suffix}"
                                        counter += 1
                                    
                                    with open(file_path, 'wb') as f:
                                        f.write(file_data)
                                    
                                    uploaded_files.append(file_path.name)
                                    print(f"📁 Uploaded: {file_path.name} ({len(file_data)} bytes)")
                                    
                                    # Invalidate cache after upload
                                    self.directory_cache.invalidate(Path(self.share_path))
                                    
                                except Exception as e:
                                    print(f"❌ Failed to save {filename}: {e}")
                                    continue
                
            except Exception as e:
                self.send_error(400, f"Failed to parse upload: {e}")
                return
            
            # Send success response
            if uploaded_files:
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                response = json.dumps({'status': 'success', 'files': uploaded_files})
                self.wfile.write(response.encode())
            else:
                self.send_error(400, "No files uploaded")
                
        except Exception as e:
            print(f"❌ Upload error: {e}")
            self.send_error(500, f"Upload error: {e}")
    
    def _sanitize_filename(self, filename):
        """Basic filename sanitization with better error handling"""
        if not filename:
            return f"upload_{int(time.time())}"
            
        import re
        # Remove path components and dangerous characters
        filename = os.path.basename(filename)
        filename = re.sub(r'[<>:"/\\|?*\x00-\x1f]', '_', filename)
        
        # Remove leading/trailing dots and spaces
        filename = filename.strip('. ')
        
        # Ensure reasonable length
        if len(filename) > 200:
            name, ext = os.path.splitext(filename)
            filename = name[:200-len(ext)] + ext
            
        # Fallback if still empty
        if not filename:
            filename = f"upload_{int(time.time())}"
            
        return filename
    
    def _format_size(self, size):
        """Format file size for display"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} PB"
    
    def _get_file_icon(self, ext):
        """Get emoji icon for file type"""
        icons = {
            '.pdf': '📄', '.doc': '📄', '.docx': '📄', '.txt': '📝',
            '.jpg': '🖼️', '.jpeg': '🖼️', '.png': '🖼️', '.gif': '🖼️',
            '.mp4': '🎬', '.avi': '🎬', '.mov': '🎬', '.mkv': '🎬',
            '.mp3': '🎵', '.wav': '🎵', '.flac': '🎵', '.m4a': '🎵',
            '.zip': '📦', '.rar': '📦', '.7z': '📦', '.tar': '📦',
            '.py': '🐍', '.js': '📜', '.html': '🌐', '.css': '🎨',
        }
        return icons.get(ext, '📄')
    
    def _send_stats(self):
        """Send server statistics"""
        stats = {
            'active_connections': threading.active_count(),
            'uptime': time.time() - getattr(self.server, 'start_time', time.time()),
        }
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(stats).encode())
    
    def _open_explorer(self):
        """Open file explorer at the share directory"""
        try:
            import platform
            import subprocess
            
            share_path = Path(self.share_path).resolve()
            system = platform.system()
            
            if system == 'Windows':
                subprocess.run(['explorer', str(share_path)], check=False)
                explorer_name = "File Explorer"
            elif system == 'Darwin':  # macOS
                subprocess.run(['open', str(share_path)], check=False)
                explorer_name = "Finder"
            elif system == 'Linux':
                subprocess.run(['xdg-open', str(share_path)], check=False)
                explorer_name = "File Manager"
            else:
                raise OSError(f"Unsupported operating system: {system}")
            
            # Send success response
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            response = json.dumps({
                'status': 'success', 
                'message': f'Opened {share_path} in {explorer_name}',
                'path': str(share_path),
                'system': system
            })
            self.wfile.write(response.encode())
            
            print(f"📁 Opened {explorer_name}: {share_path}")
            
        except Exception as e:
            print(f"❌ Failed to open explorer: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            response = json.dumps({
                'status': 'error', 
                'message': f'Failed to open explorer: {str(e)}'
            })
            self.wfile.write(response.encode())
    
    def _purge_directory(self):
        """Purge all files from the main directory"""
        try:
            print(f"🗑️ Purge request received from {self.client_address[0]}")
            
            share_path = Path(self.share_path).resolve()
            
            if not share_path.exists():
                raise FileNotFoundError(f"Share directory does not exist: {share_path}")
            
            if not share_path.is_dir():
                raise ValueError(f"Share path is not a directory: {share_path}")
            
            # Count files before deletion
            files_to_delete = []
            directories_to_delete = []
            total_size = 0
            
            print(f"🔍 Scanning directory: {share_path}")
            
            # Scan the directory
            try:
                for item in share_path.iterdir():
                    if item.is_file():
                        try:
                            size = item.stat().st_size
                            files_to_delete.append((item, size))
                            total_size += size
                        except OSError:
                            files_to_delete.append((item, 0))
                    elif item.is_dir():
                        directories_to_delete.append(item)
            except OSError as e:
                raise OSError(f"Failed to scan directory: {e}")
            
            print(f"📊 Found {len(files_to_delete)} files and {len(directories_to_delete)} directories")
            print(f"📊 Total size to delete: {self._format_size(total_size)}")
            
            # Delete files
            deleted_files = 0
            deleted_dirs = 0
            errors = []
            
            # Delete regular files
            for file_path, file_size in files_to_delete:
                try:
                    file_path.unlink()
                    deleted_files += 1
                    print(f"🗑️ Deleted file: {file_path.name} ({self._format_size(file_size)})")
                except Exception as e:
                    error_msg = f"Failed to delete {file_path.name}: {e}"
                    errors.append(error_msg)
                    print(f"❌ {error_msg}")
            
            # Delete empty directories (optional - can be disabled if you only want files)
            for dir_path in directories_to_delete:
                try:
                    if not any(dir_path.iterdir()):  # Only delete if empty
                        dir_path.rmdir()
                        deleted_dirs += 1
                        print(f"🗑️ Deleted empty directory: {dir_path.name}")
                    else:
                        print(f"⏭️ Skipped non-empty directory: {dir_path.name}")
                except Exception as e:
                    error_msg = f"Failed to delete directory {dir_path.name}: {e}"
                    errors.append(error_msg)
                    print(f"❌ {error_msg}")
            
            # Invalidate cache
            self.directory_cache.invalidate(share_path)
            
            # Prepare response message
            if deleted_files > 0 or deleted_dirs > 0:
                message_parts = []
                if deleted_files > 0:
                    message_parts.append(f"Deleted {deleted_files} files ({self._format_size(total_size)})")
                if deleted_dirs > 0:
                    message_parts.append(f"Deleted {deleted_dirs} empty directories")
                
                message = ". ".join(message_parts)
                
                if errors:
                    message += f". {len(errors)} errors occurred"
                
                print(f"✅ Purge completed: {message}")
            else:
                message = "Directory was already empty"
                print(f"ℹ️ {message}")
            
            # Send success response
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            response = json.dumps({
                'status': 'success',
                'message': message,
                'deleted_files': deleted_files,
                'deleted_directories': deleted_dirs,
                'total_size_deleted': total_size,
                'errors': errors,
                'path': str(share_path)
            })
            self.wfile.write(response.encode())
            
        except Exception as e:
            error_msg = f"Purge operation failed: {str(e)}"
            print(f"❌ {error_msg}")
            
            try:
                self.send_response(500)
                self.send_header('Content-Type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                response = json.dumps({
                    'status': 'error',
                    'message': error_msg
                })
                self.wfile.write(response.encode())
            except Exception as e2:
                print(f"❌ Failed to send error response: {e2}")

def start_mdns_service(port, service_name="RoraFTP"):
    """Start mDNS service for auto-discovery"""
    if not ServiceInfo or not Zeroconf:
        safe_print("📡 Auto-discovery not available (zeroconf not installed)")
        return None

    try:
        local_ip = get_local_ip()
        service_type = "_http._tcp.local."
        name = f"{service_name}.{service_type}"

        info = ServiceInfo(
            service_type,
            name,
            addresses=[socket.inet_aton(local_ip)],
            port=port,
            properties={
                b"path": b"/",
                b"description": b"RoraFTP LAN File Server",
            }
        )

        zconf = Zeroconf()
        zconf.register_service(info)

        safe_print(f"📡 Auto-discovery enabled: {name}")
        return zconf

    except Exception as e:
        safe_print(f"📡 Auto-discovery failed: {e}")
        return None

def get_local_ip():
    """Get primary local IP address"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(('**************', 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return '127.0.0.1'

def generate_qr_code(url):
    """Generate QR code for mobile access"""
    if not qrcode:
        return None
        
    try:
        qr = qrcode.QRCode(version=1, box_size=2, border=1)
        qr.add_data(url)
        qr.make(fit=True)
        
        # ASCII QR for terminal
        modules = qr.get_matrix()
        qr_lines = []
        for row in modules:
            line = ''.join('██' if cell else '  ' for cell in row)
            qr_lines.append(line)
        
        return '\n'.join(qr_lines)
    except Exception as e:
        safe_print(f"⚠️ QR code generation failed: {e}")
        return None

def check_system_requirements():
    """Check and report system capabilities"""
    import platform
    safe_print("\n🔍 System Check:")

    # Python version
    if sys.version_info >= (3, 6):
        safe_print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} (Good)")
    else:
        safe_print(f"⚠️ Python {sys.version_info.major}.{sys.version_info.minor} (Upgrade recommended)")

    # Operating system optimizations
    os_name = platform.system()
    if os_name == 'Windows':
        safe_print(f"✅ Windows detected - using conservative socket settings")
    else:
        safe_print(f"✅ {os_name} detected - using aggressive optimizations")

    # Optional features
    features = []
    if qrcode:
        features.append("QR Codes")
    if ServiceInfo and Zeroconf:
        features.append("Auto-discovery")

    if features:
        safe_print(f"✅ Optional features: {', '.join(features)}")
    else:
        safe_print("ℹ️ Running with basic features only")

    # Network check
    try:
        local_ip = get_local_ip()
        if local_ip != '127.0.0.1':
            safe_print(f"✅ Network: {local_ip}")
        else:
            safe_print("⚠️ Network: Only localhost available")
    except:
        safe_print("⚠️ Network: Connection issues detected")

    print()

def main():
    # Check system requirements
    check_system_requirements()
    
    parser = argparse.ArgumentParser(description='⚡ RoraFTP LAN Edition')
    parser.add_argument('--port', type=int, default=8000, help='HTTP port')
    parser.add_argument('--share', default=str(Path.home() / 'FileShare'), help='Share folder')
    parser.add_argument('--no-browser', action='store_true', help="Don't open browser")
    args = parser.parse_args()
    
    # Create share folder
    share_path = Path(args.share)
    try:
        share_path.mkdir(parents=True, exist_ok=True)
        safe_print(f"📁 Share folder: {share_path}")
    except Exception as e:
        safe_print(f"❌ Failed to create share folder: {e}")
        return
    
    # Start mDNS service
    zconf = start_mdns_service(args.port)
    
    # Create server
    try:
        def handler_factory(*handler_args, **kwargs):
            return LANFileHandler(*handler_args, share_path=str(share_path), **kwargs)
        
        server = LANOptimizedServer(('0.0.0.0', args.port), handler_factory)
        server.start_time = time.time()
        
        # Start server in background
        server_thread = threading.Thread(target=server.serve_forever, daemon=True)
        server_thread.start()
        
        # Test that server is actually running
        time.sleep(0.5)
        try:
            import urllib.request
            urllib.request.urlopen(f"http://127.0.0.1:{args.port}", timeout=1)
            safe_print(f"✅ Server started on port {args.port}")
        except:
            safe_print(f"✅ Server started on port {args.port} (test failed, but probably working)")

    except OSError as e:
        if "Address already in use" in str(e):
            safe_print(f"❌ Port {args.port} is already in use. Try a different port with --port XXXX")
        else:
            safe_print(f"❌ Failed to start server: {e}")
        return
    except Exception as e:
        safe_print(f"❌ Server error: {e}")
        return
    
    # Display info
    local_ip = get_local_ip()
    url = f"http://{local_ip}:{args.port}"
    
    print("\n" + "="*60)
    safe_print("⚡ RoraFTP LAN Edition - Maximum Speed Server")
    print("="*60)
    safe_print(f"📁 Share folder: {share_path}")
    safe_print(f"🌐 URL: {url}")
    safe_print(f"📱 Mobile: Scan QR or visit {url}")

    # Show QR code
    qr = generate_qr_code(url)
    if qr:
        safe_print(f"\n📱 QR Code for mobile access:")
        try:
            print(qr)
        except UnicodeEncodeError:
            print("QR code contains characters that cannot be displayed in this terminal")

    safe_print(f"\n⚡ LAN Optimizations:")
    print(f"   • {LAN_CHUNK_SIZE//1024//1024}MB chunks for gigabit speeds")
    print(f"   • {MAX_CONCURRENT} concurrent connections")
    print(f"   • Smart socket buffers for your OS")
    print(f"   • Zero security overhead")
    print(f"   • Auto-discovery enabled")

    safe_print(f"\n🎯 Perfect for:")
    print(f"   • iPhone/iPad (Safari, Files app)")
    print(f"   • Android (Chrome, file managers)")
    print(f"   • Windows/Mac/Linux browsers")
    print(f"   • Smart TVs and media players")

    safe_print(f"\n🗑️ NEW: Purge Directory Feature")
    print(f"   • Double confirmation required")
    print(f"   • Comprehensive logging")
    print(f"   • Safe deletion with error handling")

    import platform
    if platform.system() == 'Windows':
        safe_print(f"\nℹ️ Windows Note: Connection reset messages are normal")
        print(f"   They happen when browsers/antivirus test connections")

    print(f"\nPress Ctrl+C to stop")
    print("="*60)
    
    # Auto-open browser
    if not args.no_browser:
        threading.Timer(1.0, lambda: webbrowser.open(url)).start()
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        safe_print("\n👋 Shutting down server...")
        try:
            if zconf:
                zconf.unregister_all_services()
                zconf.close()
            server.shutdown()
            safe_print("✅ Server stopped cleanly")
        except Exception as e:
            safe_print(f"⚠️ Cleanup error: {e}")
    except Exception as e:
        safe_print(f"❌ Unexpected error: {e}")
        if zconf:
            try:
                zconf.unregister_all_services()
                zconf.close()
            except:
                pass

if __name__ == "__main__":
    main()